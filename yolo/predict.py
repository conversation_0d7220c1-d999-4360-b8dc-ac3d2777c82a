from ultralytics import YOLO
import os
import cv2
import numpy as np

# Load a model
model = YOLO("/home/<USER>/llm_project/yolo_project/code/yolo/runs/detect/yolo12n-640-zhuliao4/weights/best.pt")  # pretrained YOLO11n model

# 确保predict_result文件夹存在
predict_result_dir = "predict_result4"
if not os.path.exists(predict_result_dir):
    os.makedirs(predict_result_dir)

# 读取图片文件夹路径
image_folder = "/home/<USER>/llm_project/yolo_project/datasets/竹料/2025-07/竹料数据-pre"

# 获取文件夹中所有图片文件
image_files = [os.path.join(image_folder, f) for f in os.listdir(image_folder) if f.endswith(('.jpg', '.jpeg', '.png', '.bmp'))]

# 使用模型预测所有图片
results = model(image_files, conf=0.25, iou=0.7, max_det=3)

# Process results list
for i, result in enumerate(results):
    # 获取原始图片文件名（不含路径）
    original_filename = os.path.basename(image_files[i])
    # 创建保存路径
    save_path = os.path.join(predict_result_dir, original_filename)
    
    print(f"\n处理图片: {original_filename}")
    print(f"检测到 {len(result.boxes)} 个目标")
    
    # 访问检测结果的详细信息
    if result.boxes is not None and len(result.boxes) > 0:
        # 获取边界框的不同格式
        xywh = result.boxes.xywh  # center-x, center-y, width, height
        xywhn = result.boxes.xywhn  # normalized center-x, center-y, width, height
        xyxy = result.boxes.xyxy  # top-left-x, top-left-y, bottom-right-x, bottom-right-y
        xyxyn = result.boxes.xyxyn  # normalized top-left-x, top-left-y, bottom-right-x, bottom-right-y
        
        # 获取类别名称和置信度
        names = [result.names[cls.item()] for cls in result.boxes.cls.int()]  # class name of each box
        confs = result.boxes.conf  # confidence score of each box
        
        # 打印每个检测结果的详细信息
        for j in range(len(result.boxes)):
            print(f"  目标 {j+1}:")
            print(f"    类别: {names[j]}")
            print(f"    置信度: {confs[j]:.3f}")
            print(f"    边界框 (xyxy): {xyxy[j].tolist()}")
            print(f"    边界框 (xywh): {xywh[j].tolist()}")
            print(f"    归一化边界框 (xyxyn): {xyxyn[j].tolist()}")
            print(f"    归一化边界框 (xywhn): {xywhn[j].tolist()}")
    
    # 其他可能的输出
    boxes = result.boxes  # Boxes object for bounding box outputs
    masks = result.masks  # Masks object for segmentation masks outputs
    keypoints = result.keypoints  # Keypoints object for pose outputs
    probs = result.probs  # Probs object for classification outputs
    obb = result.obb  # Oriented boxes object for OBB outputs
    
    # 显示在屏幕上（可以根据需要注释掉）
    # result.show()
    
    # 将结果保存到predict_result文件夹中
    result.save(filename=save_path)  # save to disk
    
    # 可选：保存详细的检测结果到文本文件
    # txt_save_path = os.path.join(predict_result_dir, f"{os.path.splitext(original_filename)[0]}_results.txt")
    # with open(txt_save_path, 'w', encoding='utf-8') as f:
    #     f.write(f"图片: {original_filename}\n")
    #     f.write(f"检测到 {len(result.boxes)} 个目标\n\n")
        
    #     if result.boxes is not None and len(result.boxes) > 0:
    #         for j in range(len(result.boxes)):
    #             f.write(f"目标 {j+1}:\n")
    #             f.write(f"  类别: {names[j]}\n")
    #             f.write(f"  置信度: {confs[j]:.3f}\n")
    #             f.write(f"  边界框 (xyxy): {xyxy[j].tolist()}\n")
    #             f.write(f"  边界框 (xywh): {xywh[j].tolist()}\n")
    #             f.write(f"  归一化边界框 (xyxyn): {xyxyn[j].tolist()}\n")
    #             f.write(f"  归一化边界框 (xywhn): {xywhn[j].tolist()}\n\n")

print(f"\n所有预测结果已保存到 {predict_result_dir} 文件夹中")
print("每个图片的详细检测信息也已保存为对应的文本文件")