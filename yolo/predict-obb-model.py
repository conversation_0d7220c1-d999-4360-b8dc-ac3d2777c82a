"""
YOLO OBB (Oriented Bounding Box) 预测脚本
功能：使用训练好的 OBB 模型对图像进行旋转边界框检测
适用于：任意角度目标检测，如竹料厂区的耙斗井等大件物体的检测
作者：AI Assistant
日期：2025-07-09
"""

from ultralytics import YOLO
import os

# ================================ 模型配置 ================================
# 加载训练好的 OBB 模型
model_path = "runs/obb/yolo11n-640-datansha-piezha2/weights/best.pt"
model = YOLO(model_path)  # 加载 OBB 模型

print(f"已加载 OBB 模型: {model_path}")

# ================================ 路径配置 ================================
# 输入图像文件夹路径
image_folder = "/home/<USER>/llm_project/yolo_project/datasets/大坦沙/project-12-at-2025-07-24-02-59-88f0cb3e/test"
# 预测结果保存目录
predict_result_dir = "yolo11n-640-datansha-piezha2"

# 确保预测结果文件夹存在
if not os.path.exists(predict_result_dir):
    os.makedirs(predict_result_dir)
    print(f"创建预测结果目录: {predict_result_dir}")

# ================================ 扫描图像文件 ================================
# 支持的图像格式（扩展名元组）
supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp', 
                     '.JPG', '.JPEG', '.PNG', '.BMP', '.TIFF', '.TIF', '.WEBP')

# 获取文件夹中所有支持格式的图像文件（类似原始代码的写法）
image_files = [
    os.path.join(image_folder, f) 
    for f in os.listdir(image_folder) 
    if f.endswith(supported_formats)
]

print(f"找到 {len(image_files)} 个图像文件")

if len(image_files) == 0:
    print(f"错误：在 {image_folder} 中未找到任何图像文件")
    print(f"支持的格式: {supported_formats}")
    exit(1)
# ================================ 批量预测 ================================
# OBB 模型预测参数设置
print("开始进行 OBB 预测...")
results = model(
    image_files, 
    conf=0.25,      # 置信度阈值 设置检测的最小置信度阈值。如果检测到的对象置信度低于此阈值，则将不予考虑。调整该值有助于减少误报。
    iou=0.2,        # NMS IoU 阈值 非最大抑制 (NMS) 的交叉重叠(IoU) 阈值。较低的数值可以消除重叠的方框，从而减少检测次数，这对减少重复检测非常有用。
    max_det=3,      # 每张图最大检测数量
    save=False,     # 不自动保存，手动控制保存
    verbose=True    # 显示预测过程
)

# ================================ 处理 OBB 结果 ================================
print(f"开始处理 {len(results)} 个预测结果...")
successful_saves = 0
failed_saves = 0

for i, result in enumerate(results):
    try:
        # 获取原始图片文件名（不含路径）
        original_filename = os.path.basename(image_files[i])
        base_name, ext = os.path.splitext(original_filename)
        
        # 创建保存路径
        save_path = os.path.join(predict_result_dir, f"{base_name}_obb{ext}")
        
        # ======================== OBB 结果分析 ========================
        # 检查是否有 OBB 检测结果
        if result.obb is not None:
            obb_count = len(result.obb)
            print(f"图像 {original_filename}: 检测到 {obb_count} 个旋转边界框")
            
            # 打印 OBB 详细信息
            if obb_count > 0:
                print(f"  - 置信度: {result.obb.conf.tolist()}")
                print(f"  - 类别: {result.obb.cls.tolist()}")
                # 旋转边界框坐标 (xywhr格式: x_center, y_center, width, height, rotation)
                if hasattr(result.obb, 'xywhr'):
                    print(f"  - 旋转框 (xywhr): {result.obb.xywhr.tolist()}")
        else:
            print(f"图像 {original_filename}: 未检测到目标")
        
        # ======================== 保存结果 ========================
        # 保存带有 OBB 框的结果图像
        result.save(filename=save_path)
        successful_saves += 1
        print(f"✓ 已保存: {save_path}")
        
    except Exception as e:
        failed_saves += 1
        print(f"✗ 保存失败 {original_filename}: {str(e)}")

# ================================ 输出统计信息 ================================
print("\n" + "="*60)
print("OBB 预测完成！统计信息如下：")
print("="*60)
print(f"总处理图像: {len(image_files)} 张")
print(f"成功保存: {successful_saves} 张")
print(f"保存失败: {failed_saves} 张")
print(f"结果保存目录: {predict_result_dir}")
print("="*60)
print("OBB 模型特点：")
print("- 支持任意角度的目标检测")
print("- 返回旋转边界框而非水平边界框")  
print("- 适用于倾斜物体（如文本、竹料等）的检测")
print("="*60)