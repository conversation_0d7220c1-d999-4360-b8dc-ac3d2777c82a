'''
yolo11x.pt 15-16G显存
yolo11n.pt 3-5G显存
'''


from ultralytics import YOLO
from swanlab.integration.ultralytics import add_swanlab_callback
# Load a model
# model = YOLO("yolo11n.yaml")  # build a new model from YAML
# model = YOLO("yolo12n.t")  # load a pretrained model (recommended for training)
model = YOLO("yolo11n-obb.pt")  # 使用obb旋转位置编码模型进行训练
add_swanlab_callback(model)
if __name__ == '__main__':

    # Train the model with augmentation
    results = model.train(
        data="data.yaml", 
        epochs=300, 
        imgsz=640,
        name="yolo11n-640-datansha-piezha",
        # 数据增强参数
        hsv_h=0.015,  # HSV色调增强因子
        hsv_s=0.7,    # HSV饱和度增强因子
        hsv_v=0.4,    # HSV值增强因子
        degrees=10.0,  # 图像旋转角度 ±10度
        translate=0.1, # 平移增强 ±10%
        scale=0.5,    # 缩放增强 ±50%
        shear=2.0,    # 剪切增强 ±2度
        # flipud=0.0,   # 上下翻转概率 (不建议用于目标检测)
        fliplr=0.5,   # 左右翻转概率 50%
        mosaic=1.0,   # 马赛克增强概率 100%
        mixup=0.1     # mixup增强概率 10%
    )
    
    # Validate the model
    # metrics = model.val(data="data.yaml")  # assumes `model` has been loaded
    # print(metrics.box.map)    # mAP50-95
    # print(metrics.box.map50)  # mAP50
    # print(metrics.box.map75)  # mAP75
    # print(metrics.box.maps)   # list of mAP50-95 for each category

"""
        # 日志
    #     Validating runs\detect\train\weights\best.pt...
    # Ultralytics 8.3.78 🚀 Python-3.10.16 torch-2.4.1+cu121 CUDA:0 (NVIDIA GeForce RTX 4070 Ti SUPER, 16376MiB)
    # YOLO11n summary (fused): 100 layers, 2,582,542 parameters, 0 gradients, 6.3 GFLOPs
    #                  Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 5/5 [00:00<00:00,  7.68it/s]
    #                    all        145        359      0.903      0.856      0.891      0.666
    #                incline          7          7      0.824      0.714      0.788       0.51
    #             no_incline        145        352      0.982      0.997      0.994      0.823
    # Speed: 0.1ms preprocess, 0.4ms inference, 0.0ms loss, 1.0ms postprocess per image
    # Results saved to runs\detect\train
    # Ultralytics 8.3.78 🚀 Python-3.10.16 torch-2.4.1+cu121 CUDA:0 (NVIDIA GeForce RTX 4070 Ti SUPER, 16376MiB)
    # YOLO11n summary (fused): 100 layers, 2,582,542 parameters, 0 gradients, 6.3 GFLOPs
    # val: Scanning E:\工作\E9\code_test\yolo\datasets\val\labels.cache... 145 images, 0 backgrounds, 0 corrupt: 100%|██████████| 145/145 [00:00<?, ?it/s]
    #                  Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:00<00:00, 11.40it/s]
    #                    all        145        359      0.901      0.856      0.891      0.673
    #                incline          7          7      0.821      0.714      0.788      0.522
    #             no_incline        145        352      0.982      0.997      0.994      0.824
    # Speed: 0.4ms preprocess, 2.0ms inference, 0.0ms loss, 0.9ms postprocess per image
    # Results saved to runs\detect\train2
    # 0.672946096547567
    # 0.8909948790371476
    # 0.7441738943573276
"""
# ************************************ 增强 ************************************ #
"""
    Validating runs\detect\yolo11n\weights\best.pt...
    Ultralytics 8.3.78 🚀 Python-3.10.16 torch-2.4.1+cu121 CUDA:0 (NVIDIA GeForce RTX 4070 Ti SUPER, 16376MiB)
    YOLO11n summary (fused): 100 layers, 2,582,542 parameters, 0 gradients, 6.3 GFLOPs
                    Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 5/5 [00:00<00:00,  8.70it/s]
                    all        145        359      0.988      0.827       0.91      0.706
                incline          7          7          1      0.655      0.827      0.603
                no_incline        145        352      0.977          1      0.993      0.808
    Speed: 0.0ms preprocess, 0.4ms inference, 0.0ms loss, 0.9ms postprocess per image
    Results saved to runs\detect\yolo11n
    Ultralytics 8.3.78 🚀 Python-3.10.16 torch-2.4.1+cu121 CUDA:0 (NVIDIA GeForce RTX 4070 Ti SUPER, 16376MiB)
    YOLO11n summary (fused): 100 layers, 2,582,542 parameters, 0 gradients, 6.3 GFLOPs
    val: Scanning E:\工作\E9\code_test\yolo\datasets\val\labels.cache... 145 images, 0 backgrounds, 0 corrupt: 100%|██████████| 145/145 [00:00<?, ?it/s]
                    Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:00<00:00, 13.27it/s]
                    all        145        359      0.988       0.83       0.91      0.692
                incline          7          7          1       0.66      0.827      0.575
                no_incline        145        352      0.977          1      0.993      0.808
    Speed: 0.2ms preprocess, 1.4ms inference, 0.0ms loss, 1.1ms postprocess per image
    Results saved to runs\detect\yolo11n2
    0.6918896029435609
    0.9099598389470243
    0.79817730168673
    [    0.57543     0.80835]
"""

"""
YOLO11x summary (fused): 190 layers, 56,829,334 parameters, 0 gradients, 194.4 GFLOPs
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 5/5 [00:00<00:00,  5.35it/s
                   all        145        359      0.798      0.786      0.819      0.589
               incline          7          7      0.617      0.571      0.645      0.366
            no_incline        145        352       0.98          1      0.992      0.811
Speed: 0.1ms preprocess, 3.6ms inference, 0.0ms loss, 0.6ms postprocess per image
"""