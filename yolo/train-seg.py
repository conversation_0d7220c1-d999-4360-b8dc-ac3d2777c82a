from ultralytics import YOLO
from swanlab.integration.ultralytics import add_swanlab_callback
# Load a model
# model = YOLO("yolo11n-seg.yaml")  # build a new model from YAML
# model = YOLO("yolo11n-seg.pt")  # load a pretrained model (recommended for training)
# model = YOLO("yolo11n-seg.yaml").load("yolo11n.pt")  # build from YAML and transfer weights

model = YOLO("yolo11n-seg.pt")  # load a pretrained model (recommended for training)
add_swanlab_callback(model)
# Train the model
results = model.train(data="data-seg.yaml", epochs=100, imgsz=640)