from ultralytics import YOLO
from swanlab.integration.ultralytics import add_swanlab_callback

# Load a model
# model = YOLO("yolo11n-seg.yaml")  # build a new model from YAML
# model = YOLO("yolo11n-seg.pt")  # load a pretrained model (recommended for training)
# model = YOLO("yolo11n-seg.yaml").load("yolo11n.pt")  # build from YAML and transfer weights

model = YOLO("yolo11n-seg.pt")  # load a pretrained model (recommended for training)
add_swanlab_callback(model)

# Train the model with enhanced parameters for stability and generalization
results = model.train(
    # 基础训练参数
    data="data-seg.yaml",           # 数据集配置文件
    epochs=300,                     # 训练轮数，从100增加到300以获得更好的收敛
    imgsz=640,                      # 输入图像大小

    # 批次和设备优化
    batch=-1,                       # 自动确定最优批次大小，充分利用GPU内存
    device=0,                       # 使用第一个GPU，如果有多个GPU可以设置为[0,1]
    workers=8,                      # 数据加载线程数

    # 训练稳定性参数
    amp=True,                       # 启用自动混合精度训练，提升训练速度并减少内存使用
    patience=50,                    # 早停耐心值，50个epoch无改善则停止训练
    save=True,                      # 保存训练检查点
    save_period=10,                 # 每10个epoch保存一次检查点

    # 学习率和优化器设置
    optimizer='AdamW',              # 使用AdamW优化器，通常比SGD更稳定
    lr0=0.001,                      # 初始学习率，对分割任务适中的学习率
    lrf=0.01,                       # 最终学习率因子
    momentum=0.937,                 # 动量参数
    weight_decay=0.0005,            # 权重衰减，防止过拟合
    warmup_epochs=3.0,              # 学习率预热轮数
    warmup_momentum=0.8,            # 预热阶段动量
    warmup_bias_lr=0.1,             # 预热阶段偏置学习率

    # 泛化性增强参数
    multi_scale=True,               # 启用多尺度训练，提高模型对不同尺寸目标的适应性
    cos_lr=True,                    # 使用余弦学习率调度器
    close_mosaic=10,                # 在最后10个epoch关闭mosaic增强，稳定训练

    # 数据增强参数（增强泛化能力）
    hsv_h=0.015,                    # 色调增强
    hsv_s=0.7,                      # 饱和度增强
    hsv_v=0.4,                      # 明度增强
    degrees=0.0,                    # 旋转角度（分割任务通常不使用旋转）
    translate=0.1,                  # 平移增强
    scale=0.5,                      # 缩放增强，提高对不同尺寸目标的鲁棒性
    shear=0.0,                      # 剪切变换（分割任务通常不使用）
    perspective=0.0,                # 透视变换（分割任务通常不使用）
    flipud=0.0,                     # 上下翻转概率
    fliplr=0.5,                     # 左右翻转概率
    mosaic=1.0,                     # Mosaic增强概率
    mixup=0.0,                      # Mixup增强（分割任务通常不使用）
    copy_paste=0.1,                 # 分割专用：复制粘贴增强

    # 性能优化参数
    cache=True,                     # 启用数据缓存，加速训练
    rect=False,                     # 矩形训练（分割任务通常关闭）

    # 损失函数权重（针对分割任务优化）
    box=7.5,                        # 边界框损失权重
    cls=0.5,                        # 分类损失权重
    dfl=1.5,                        # 分布焦点损失权重

    # 分割特定参数
    overlap_mask=True,              # 允许掩码重叠
    mask_ratio=4,                   # 掩码下采样率

    # 验证和可视化
    val=True,                       # 启用验证
    plots=True,                     # 生成训练图表

    # 项目管理
    project='runs/segment',         # 项目目录
    name='water_segmentation',      # 实验名称
    exist_ok=True,                  # 允许覆盖现有目录

    # 其他稳定性参数
    seed=42,                        # 随机种子，确保可重复性
    deterministic=True,             # 使用确定性算法
    single_cls=False,               # 多类别训练
    verbose=True                    # 详细输出
)