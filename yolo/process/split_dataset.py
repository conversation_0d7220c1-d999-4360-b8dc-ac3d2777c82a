"""
YOLO数据集划分脚本
功能：将标注好的图像数据集按照8:2的比例划分为训练集和验证集
适用于：YOLO目标检测模型的数据预处理
作者：AI Assistant
日期：2025-07-09
"""

import os
import shutil
from sklearn.model_selection import train_test_split
import glob

# ================================ 路径配置 ================================
# 数据集根目录路径
dataset_root = "/home/<USER>/llm_project/yolo_project/datasets/大坦沙/好氧池面积/project-14-at-2025-08-15-08-36-dff0f5af"

# 原始数据路径
image_dir = os.path.join(dataset_root, "images")        # 原始图像文件夹路径
label_dir = os.path.join(dataset_root, "labels")        # 原始标签文件夹路径（YOLO格式的.txt标注文件）

# 输出数据路径
train_dir = os.path.join(dataset_root, "train")         # 训练集输出目录
val_dir = os.path.join(dataset_root, "val")             # 验证集输出目录

# ================================ 创建目录结构 ================================
# 创建YOLO标准的数据集目录结构：
# train/
#   ├── images/  (训练图像)
#   └── labels/  (训练标签)
# val/
#   ├── images/  (验证图像)
#   └── labels/  (验证标签)
for dir_path in [
    os.path.join(train_dir, "images"),    # 训练集图像目录
    os.path.join(train_dir, "labels"),    # 训练集标签目录
    os.path.join(val_dir, "images"),      # 验证集图像目录
    os.path.join(val_dir, "labels")       # 验证集标签目录
]:
    os.makedirs(dir_path, exist_ok=True)  # exist_ok=True表示目录已存在时不报错

# ================================ 扫描图像文件 ================================
# 支持的图像格式列表（常见的计算机视觉格式）
image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff", "*.tif", "*.webp"]
image_files = []  # 存储所有找到的图像文件路径

# 遍历所有支持的图像格式
for ext in image_extensions:
    # 使用glob模块查找匹配的文件（小写扩展名）
    image_files.extend(glob.glob(os.path.join(image_dir, ext)))
    # 同时检查大写扩展名（兼容不同命名习惯）
    image_files.extend(glob.glob(os.path.join(image_dir, ext.upper())))

print(f"找到 {len(image_files)} 个图像文件")

# ================================ 提取文件信息 ================================
# 提取每个图像文件的基础文件名和扩展名
# 这样做是为了后续能够正确匹配图像文件和对应的标签文件
file_info = []
for img_file in image_files:
    # 获取不含扩展名的基础文件名（例如：image001.jpg -> image001）
    base_name = os.path.splitext(os.path.basename(img_file))[0]
    # 获取文件扩展名（例如：image001.jpg -> .jpg）
    ext = os.path.splitext(os.path.basename(img_file))[1]
    # 存储为元组：(基础文件名, 扩展名)
    file_info.append((base_name, ext))

# 提取所有基础文件名，用于数据集划分
base_names = [info[0] for info in file_info]

# ================================ 数据集划分 ================================
# 使用sklearn的train_test_split函数按照8:2比例划分数据集
# test_size=0.2 表示20%作为验证集，80%作为训练集
# random_state=42 设置随机种子，确保每次运行结果一致（可复现性）
train_names, val_names = train_test_split(base_names, test_size=0.2, random_state=42)

# ================================ 文件复制函数 ================================
def copy_files(file_names, split_type):
    """
    将指定的文件复制到对应的数据集分割目录中

    参数:
        file_names (list): 要复制的文件基础名称列表
        split_type (str): 数据集类型，"train" 或 "val"

    功能:
        1. 复制图像文件到对应的images目录
        2. 复制标签文件到对应的labels目录
        3. 处理文件不存在的情况并给出提示
    """
    # 创建基础文件名到完整信息的映射字典
    # 这样可以快速查找每个文件名对应的扩展名信息
    name_to_info = {info[0]: info for info in file_info}

    # 遍历要复制的每个文件
    for name in file_names:
        if name in name_to_info:
            # 获取文件的基础名称和扩展名
            base_name, ext = name_to_info[name]

            # ======================== 复制图像文件 ========================
            # 构建源图像文件路径
            src_img = os.path.join(image_dir, f"{base_name}{ext}")
            # 构建目标图像文件路径
            dst_img = os.path.join(dataset_root, split_type, "images", f"{base_name}{ext}")

            # 检查源图像文件是否存在
            if os.path.exists(src_img):
                # 使用shutil.copy2保持文件的元数据（如修改时间等）
                shutil.copy2(src_img, dst_img)
                print(f"复制图像: {base_name}{ext}")
            else:
                print(f"图像文件不存在: {src_img}")

            # ======================== 复制标签文件 ========================
            # 构建源标签文件路径（YOLO格式标签文件都是.txt格式）
            src_label = os.path.join(label_dir, f"{base_name}.txt")
            # 构建目标标签文件路径
            dst_label = os.path.join(dataset_root, split_type, "labels", f"{base_name}.txt")

            # 检查源标签文件是否存在
            if os.path.exists(src_label):
                # 复制标签文件
                shutil.copy2(src_label, dst_label)
                print(f"复制标签: {base_name}.txt")
            else:
                print(f"标签文件不存在: {src_label}")

# ================================ 执行数据集划分 ================================
print("开始复制训练集文件...")
# 复制训练集文件（图像和标签）
copy_files(train_names, "train")

print("开始复制验证集文件...")
# 复制验证集文件（图像和标签）
copy_files(val_names, "val")

# ================================ 输出统计信息 ================================
print("\n" + "="*50)
print("数据集划分完成！统计信息如下：")
print("="*50)
print(f"总数据集大小: {len(base_names)} 个样本")
print(f"训练集大小: {len(train_names)} 个样本 ({len(train_names)/len(base_names)*100:.1f}%)")
print(f"验证集大小: {len(val_names)} 个样本 ({len(val_names)/len(base_names)*100:.1f}%)")
print("="*50)
print("数据集目录结构：")
print("├── train/")
print("│   ├── images/  (训练图像)")
print("│   └── labels/  (训练标签)")
print("└── val/")
print("    ├── images/  (验证图像)")
print("    └── labels/  (验证标签)")
print("="*50)