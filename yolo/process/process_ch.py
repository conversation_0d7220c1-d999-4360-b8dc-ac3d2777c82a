# ============================================== #
#                 去除文件夹下图片文件中的中文                 #
# ============================================== #

import os
import re
import argparse

def is_chinese(char):
    """判断字符是否为中文"""
    return '\u4e00' <= char <= '\u9fff'

def remove_chinese_from_filename(filename):
    """去除文件名中的中文字符和空格"""
    # 分离文件名和扩展名
    name, ext = os.path.splitext(filename)
    
    # 保留非中文字符，同时去除空格
    new_name = ''.join([c for c in name if not is_chinese(c) and c != ' '])
    
    # 如果新文件名为空，使用默认名称
    if not new_name:
        new_name = "image"
    
    return new_name + ext

def process_directory(dir_path):
    """处理目录下所有的图片文件"""
    count = 0
    for filename in os.listdir(dir_path):
        if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
            old_path = os.path.join(dir_path, filename)
            new_filename = remove_chinese_from_filename(filename)
            
            # 如果文件名中含有中文，则重命名
            if new_filename != filename:
                new_path = os.path.join(dir_path, new_filename)
                
                # 检查是否存在同名文件
                if os.path.exists(new_path):
                    name, ext = os.path.splitext(new_filename)
                    new_path = os.path.join(dir_path, f"{name}_{count}{ext}")
                
                os.rename(old_path, new_path)
                print(f"重命名: {filename} -> {os.path.basename(new_path)}")
                count += 1
    
    print(f"总共处理了 {count} 个文件")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="去除图片文件名中的中文字符")
    parser.add_argument("--dir_path", default='/home/<USER>/llm_project/yolo_project/datasets/bad_case/耙斗负样本', help="图片所在文件夹路径")
    args = parser.parse_args()
    
    if not os.path.isdir(args.dir_path):
        print(f"错误: '{args.dir_path}' 不是一个有效的文件夹路径")
        exit(1)
    
    process_directory(args.dir_path)
