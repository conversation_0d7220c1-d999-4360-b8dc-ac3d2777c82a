import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
from PIL import Image, ImageTk
import cv2
import numpy as np
import os

class ImageExtractorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("交互式图像提取工具")
        
        self.points = []
        self.image_path = None
        self.original_cv_image = None
        self.display_image = None # 用于在Tkinter中显示
        self.tk_image = None

        # --- GUI 布局 ---
        # 主框架
        main_frame = tk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧：画布和控制按钮
        left_frame = tk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.canvas = tk.Canvas(left_frame, cursor="cross", bg="gray")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        self.canvas.bind("<Button-1>", self.on_canvas_click)

        button_frame = tk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(button_frame, text="打开图片", command=self.open_image).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="提取并保存", command=self.extract_and_save).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="重置", command=self.reset).pack(side=tk.LEFT, padx=5)

        # 右侧：坐标显示
        right_frame = tk.Frame(main_frame, width=200)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.pack_propagate(False) # 防止框架自动缩放

        tk.Label(right_frame, text="坐标点:").pack(anchor="w")
        self.coords_text = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD, height=10)
        self.coords_text.pack(fill=tk.BOTH, expand=True)

    def open_image(self):
        path = filedialog.askopenfilename(filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp")])
        if not path:
            return
        
        self.image_path = path
        self.original_cv_image = cv2.imread(self.image_path)
        if self.original_cv_image is None:
            messagebox.showerror("错误", "无法加载图片文件。")
            return
        
        self.reset() # 打开新图片时重置状态

    def update_canvas_image(self):
        # 将OpenCV图像(BGR)转换为PIL图像(RGB)
        img_rgb = cv2.cvtColor(self.display_image, cv2.COLOR_BGR2RGB)
        pil_img = Image.fromarray(img_rgb)
        
        # 调整图片大小以适应画布，同时保持纵横比
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        pil_img.thumbnail((canvas_width, canvas_height), Image.Resampling.LANCZOS)

        self.tk_image = ImageTk.PhotoImage(pil_img)
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.tk_image)
        
        # 重新绘制点和线
        for i, (x, y) in enumerate(self.points):
            self.draw_point(x, y, i + 1)
        
        if len(self.points) > 1:
            for i in range(len(self.points) - 1):
                self.canvas.create_line(self.points[i], self.points[i+1], fill="green", width=2)


    def on_canvas_click(self, event):
        if self.original_cv_image is None:
            messagebox.showwarning("提示", "请先打开一张图片。")
            return
        
        x, y = event.x, event.y
        self.points.append((x, y))
        
        # 绘制点和序号
        self.draw_point(x, y, len(self.points))
        
        # 连接线
        if len(self.points) > 1:
            self.canvas.create_line(self.points[-2], self.points[-1], fill="green", width=2)

        self.update_coords_text()

    def draw_point(self, x, y, number):
        self.canvas.create_oval(x-3, y-3, x+3, y+3, fill="red", outline="red")
        self.canvas.create_text(x+5, y-5, text=str(number), fill="yellow", anchor="nw")

    def update_coords_text(self):
        self.coords_text.delete(1.0, tk.END)
        for i, (x, y) in enumerate(self.points):
            self.coords_text.insert(tk.END, f"点 {i+1}: ({x}, {y})\n")
    
    def reset(self):
        if self.original_cv_image is None:
            return
        self.points = []
        self.display_image = self.original_cv_image.copy()
        self.update_coords_text()
        self.root.after(50, self.update_canvas_image) # 延迟更新以获取正确的画布大小

    def extract_and_save(self):
        if len(self.points) < 3:
            messagebox.showerror("错误", "请至少选择3个点来构成一个区域。")
            return
        
        save_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")],
            title="保存提取的图片"
        )
        if not save_path:
            return

        # 创建蒙版
        mask = np.zeros(self.original_cv_image.shape[:2], dtype=np.uint8)
        pts_array = np.array(self.points, dtype=np.int32)
        cv2.fillPoly(mask, [pts_array], 255)

        # 创建带透明背景的4通道图像
        b, g, r = cv2.split(self.original_cv_image)
        alpha = np.zeros(b.shape, dtype=b.dtype) # 创建一个全透明的alpha通道
        alpha[mask == 255] = 255 # 在多边形区域内设为不透明
        
        final_result = cv2.merge([b, g, r, alpha])
        
        # 保存图像和坐标
        try:
            cv2.imwrite(save_path, final_result)
            
            # 保存坐标文件
            base, _ = os.path.splitext(save_path)
            coord_path = base + "_coords.txt"
            with open(coord_path, 'w') as f:
                f.write(f"Image Source: {self.image_path}\n")
                f.write(f"Extracted Coordinates:\n")
                for i, point in enumerate(self.points):
                    f.write(f"Point {i+1}: {point[0]}, {point[1]}\n")
            
            messagebox.showinfo("成功", f"图像已保存到: {save_path}\n坐标已保存到: {coord_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存文件失败: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("800x600") # 设置初始窗口大小
    app = ImageExtractorApp(root)
    root.mainloop()